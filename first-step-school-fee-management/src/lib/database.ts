import { supabase } from './supabase'
import { Student, FeePayment } from '@/types/database'

// Get class information with names
export async function getClassesWithNames(): Promise<{id: string, name: string, section: string}[]> {
  if (typeof window !== 'undefined') {
    // Client-side: use API route
    const response = await fetch('/api/classes-with-names')
    if (!response.ok) throw new Error('Failed to fetch classes')
    return response.json()
  } else {
    // Server-side: direct database access
    const { data, error } = await supabase
      .schema('school')
      .from('Class')
      .select('id, name, section')
      .order('name, section')

    if (error) throw error
    return data || []
  }
}

// Client-side functions that use API routes
export async function getClasses(): Promise<string[]> {
  if (typeof window !== 'undefined') {
    // Client-side: use API route
    const response = await fetch('/api/classes')
    if (!response.ok) throw new Error('Failed to fetch classes')
    return response.json()
  } else {
    // Server-side: direct database access
    const { data, error } = await supabase
      .schema('school')
      .from('IDCard')
      .select('class_id')
      .order('class_id')

    if (error) throw error

    // Get unique class names
    const uniqueClasses = [...new Set(data.map(item => item.class_id).filter(Boolean))]
    return uniqueClasses
  }
}

export async function getStudentsByClass(className: string): Promise<Student[]> {
  if (typeof window !== 'undefined') {
    // Client-side: use API route
    const response = await fetch(`/api/students?class=${encodeURIComponent(className)}`)
    if (!response.ok) throw new Error('Failed to fetch students')
    return response.json()
  } else {
    // Server-side: direct database access
    const { data, error } = await supabase
      .schema('school')
      .from('IDCard')
      .select('*')
      .eq('class_id', className)
      .order('student_name')

    if (error) throw error
    return data || []
  }
}

export async function getStudentById(studentId: string): Promise<Student | null> {
  const { data, error } = await supabase
    .schema('school')
    .from('IDCard')
    .select('*')
    .eq('id', studentId)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null // No rows returned
    throw error
  }
  return data
}

// Fee payment operations
export async function createFeePayment(payment: Omit<FeePayment, 'id' | 'created_at' | 'updated_at' | 'receipt_url'>): Promise<FeePayment> {
  if (typeof window !== 'undefined') {
    // Client-side: use API route
    const response = await fetch('/api/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payment),
    })
    if (!response.ok) throw new Error('Failed to create payment')
    return response.json()
  } else {
    // Server-side: direct database access
    const receiptId = crypto.randomUUID()
    const receiptUrl = `/receipt/${receiptId}`

    const { data, error } = await supabase
      .schema('school')
      .from('fee_payments')
      .insert({
        ...payment,
        receipt_url: receiptUrl
      })
      .select('*')
      .single()

    if (error) throw error
    return data
  }
}

export async function getFeePaymentByReceiptUrl(receiptUrl: string): Promise<FeePayment | null> {
  const { data, error } = await supabase
    .schema('school')
    .from('fee_payments')
    .select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `)
    .eq('receipt_url', receiptUrl)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null // No rows returned
    throw error
  }
  return data
}

export async function getFeePaymentsByStudent(studentId: string): Promise<FeePayment[]> {
  const { data, error } = await supabase
    .schema('school')
    .from('fee_payments')
    .select('*')
    .eq('student_id', studentId)
    .order('payment_date', { ascending: false })

  if (error) throw error
  return data || []
}

export async function getAllFeePayments(): Promise<FeePayment[]> {
  const { data, error } = await supabase
    .schema('school')
    .from('fee_payments')
    .select(`
      *,
      student:IDCard(*)
    `)
    .order('payment_date', { ascending: false })

  if (error) throw error
  return data || []
}
