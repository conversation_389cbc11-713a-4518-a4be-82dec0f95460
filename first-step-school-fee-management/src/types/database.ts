export interface Student {
  id: string
  class_id: string
  student_name: string
  father_name: string
  mother_name: string
  father_mobile?: string
  mother_mobile?: string
  student_photo_url?: string
  father_photo_url?: string
  mother_photo_url?: string
  date_of_birth?: string
  address?: string
  created_at?: string
  download_count?: number
  class?: {
    name: string
    section: string
  }
}

export interface FeePayment {
  id: string
  student_id: string
  amount_received: number
  payment_date: string
  payment_method: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque'
  balance_remaining: number
  payment_status: 'completed' | 'partial' | 'pending'
  notes?: string
  receipt_url: string
  created_at: string
  updated_at: string
  // Joined student data
  student?: Student
}

export interface Database {
  school: {
    Tables: {
      IDCard: {
        Row: Student
        Insert: Omit<Student, 'id' | 'created_at' | 'download_count'>
        Update: Partial<Omit<Student, 'id' | 'created_at'>>
      }
      fee_payments: {
        Row: FeePayment
        Insert: Omit<FeePayment, 'id' | 'created_at' | 'updated_at' | 'receipt_url'>
        Update: Partial<Omit<FeePayment, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}
