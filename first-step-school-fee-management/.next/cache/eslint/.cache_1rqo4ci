[{"/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts": "1", "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts": "2", "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts": "3", "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx": "4", "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx": "5", "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx": "6", "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx": "7", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx": "8", "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx": "9", "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts": "10", "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts": "11", "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts": "12"}, {"size": 383, "mtime": 1751267327586, "results": "13", "hashOfConfig": "14"}, {"size": 998, "mtime": 1751267348010, "results": "15", "hashOfConfig": "14"}, {"size": 666, "mtime": 1751267336722, "results": "16", "hashOfConfig": "14"}, {"size": 1290, "mtime": 1751267141768, "results": "17", "hashOfConfig": "14"}, {"size": 734, "mtime": 1751267398714, "results": "18", "hashOfConfig": "14"}, {"size": 593, "mtime": 1751267205871, "results": "19", "hashOfConfig": "14"}, {"size": 1500, "mtime": 1751267279927, "results": "20", "hashOfConfig": "14"}, {"size": 12135, "mtime": 1751267259911, "results": "21", "hashOfConfig": "14"}, {"size": 8215, "mtime": 1751267315514, "results": "22", "hashOfConfig": "14"}, {"size": 3549, "mtime": 1751267383766, "results": "23", "hashOfConfig": "14"}, {"size": 413, "mtime": 1751267065628, "results": "24", "hashOfConfig": "14"}, {"size": 1156, "mtime": 1751267080852, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1187ui9", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx", ["62", "63"], [], "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx", ["64", "65"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx", ["66", "67", "68", "69", "70"], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts", [], [], {"ruleId": "71", "severity": 2, "message": "72", "line": 10, "column": 26, "nodeType": "73", "messageId": "74", "suggestions": "75"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 10, "column": 47, "nodeType": "73", "messageId": "74", "suggestions": "76"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 2, "column": 10, "nodeType": null, "messageId": "79", "endLine": 2, "endColumn": 16}, {"ruleId": "77", "severity": 2, "message": "80", "line": 53, "column": 12, "nodeType": null, "messageId": "79", "endLine": 53, "endColumn": 17}, {"ruleId": "77", "severity": 2, "message": "81", "line": 5, "column": 19, "nodeType": null, "messageId": "79", "endLine": 5, "endColumn": 27}, {"ruleId": "71", "severity": 2, "message": "72", "line": 136, "column": 55, "nodeType": "73", "messageId": "74", "suggestions": "82"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 142, "column": 55, "nodeType": "73", "messageId": "74", "suggestions": "83"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 146, "column": 55, "nodeType": "73", "messageId": "74", "suggestions": "84"}, {"ruleId": "71", "severity": 2, "message": "72", "line": 150, "column": 55, "nodeType": "73", "messageId": "74", "suggestions": "85"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["86", "87", "88", "89"], ["90", "91", "92", "93"], "@typescript-eslint/no-unused-vars", "'format' is defined but never used.", "unusedVar", "'error' is defined but never used.", "'Download' is defined but never used.", ["94", "95", "96", "97"], ["98", "99", "100", "101"], ["102", "103", "104", "105"], ["106", "107", "108", "109"], {"messageId": "110", "data": "111", "fix": "112", "desc": "113"}, {"messageId": "110", "data": "114", "fix": "115", "desc": "116"}, {"messageId": "110", "data": "117", "fix": "118", "desc": "119"}, {"messageId": "110", "data": "120", "fix": "121", "desc": "122"}, {"messageId": "110", "data": "123", "fix": "124", "desc": "113"}, {"messageId": "110", "data": "125", "fix": "126", "desc": "116"}, {"messageId": "110", "data": "127", "fix": "128", "desc": "119"}, {"messageId": "110", "data": "129", "fix": "130", "desc": "122"}, {"messageId": "110", "data": "131", "fix": "132", "desc": "113"}, {"messageId": "110", "data": "133", "fix": "134", "desc": "116"}, {"messageId": "110", "data": "135", "fix": "136", "desc": "119"}, {"messageId": "110", "data": "137", "fix": "138", "desc": "122"}, {"messageId": "110", "data": "139", "fix": "140", "desc": "113"}, {"messageId": "110", "data": "141", "fix": "142", "desc": "116"}, {"messageId": "110", "data": "143", "fix": "144", "desc": "119"}, {"messageId": "110", "data": "145", "fix": "146", "desc": "122"}, {"messageId": "110", "data": "147", "fix": "148", "desc": "113"}, {"messageId": "110", "data": "149", "fix": "150", "desc": "116"}, {"messageId": "110", "data": "151", "fix": "152", "desc": "119"}, {"messageId": "110", "data": "153", "fix": "154", "desc": "122"}, {"messageId": "110", "data": "155", "fix": "156", "desc": "113"}, {"messageId": "110", "data": "157", "fix": "158", "desc": "116"}, {"messageId": "110", "data": "159", "fix": "160", "desc": "119"}, {"messageId": "110", "data": "161", "fix": "162", "desc": "122"}, "replaceWithAlt", {"alt": "163"}, {"range": "164", "text": "165"}, "Replace with `&apos;`.", {"alt": "166"}, {"range": "167", "text": "168"}, "Replace with `&lsquo;`.", {"alt": "169"}, {"range": "170", "text": "171"}, "Replace with `&#39;`.", {"alt": "172"}, {"range": "173", "text": "174"}, "Replace with `&rsquo;`.", {"alt": "163"}, {"range": "175", "text": "176"}, {"alt": "166"}, {"range": "177", "text": "178"}, {"alt": "169"}, {"range": "179", "text": "180"}, {"alt": "172"}, {"range": "181", "text": "182"}, {"alt": "163"}, {"range": "183", "text": "184"}, {"alt": "166"}, {"range": "185", "text": "186"}, {"alt": "169"}, {"range": "187", "text": "188"}, {"alt": "172"}, {"range": "189", "text": "190"}, {"alt": "163"}, {"range": "191", "text": "192"}, {"alt": "166"}, {"range": "193", "text": "194"}, {"alt": "169"}, {"range": "195", "text": "196"}, {"alt": "172"}, {"range": "197", "text": "198"}, {"alt": "163"}, {"range": "199", "text": "200"}, {"alt": "166"}, {"range": "201", "text": "202"}, {"alt": "169"}, {"range": "203", "text": "204"}, {"alt": "172"}, {"range": "205", "text": "206"}, {"alt": "163"}, {"range": "207", "text": "208"}, {"alt": "166"}, {"range": "209", "text": "210"}, {"alt": "169"}, {"range": "211", "text": "212"}, {"alt": "172"}, {"range": "213", "text": "214"}, "&apos;", [404, 494], "\n          The receipt you&apos;re looking for doesn't exist or may have been removed.\n        ", "&lsquo;", [404, 494], "\n          The receipt you&lsquo;re looking for doesn't exist or may have been removed.\n        ", "&#39;", [404, 494], "\n          The receipt you&#39;re looking for doesn't exist or may have been removed.\n        ", "&rsquo;", [404, 494], "\n          The receipt you&rsquo;re looking for doesn't exist or may have been removed.\n        ", [404, 494], "\n          The receipt you're looking for doesn&apos;t exist or may have been removed.\n        ", [404, 494], "\n          The receipt you're looking for doesn&lsquo;t exist or may have been removed.\n        ", [404, 494], "\n          The receipt you're looking for doesn&#39;t exist or may have been removed.\n        ", [404, 494], "\n          The receipt you're looking for doesn&rsquo;t exist or may have been removed.\n        ", [5213, 5227], "Father&apos;s Name:", [5213, 5227], "Father&lsquo;s Name:", [5213, 5227], "Father&#39;s Name:", [5213, 5227], "Father&rsquo;s Name:", [5499, 5513], "Mother&apos;s Name:", [5499, 5513], "Mother&lsquo;s Name:", [5499, 5513], "Mother&#39;s Name:", [5499, 5513], "Mother&rsquo;s Name:", [5726, 5742], "Father&apos;s Mobile:", [5726, 5742], "Father&lsquo;s Mobile:", [5726, 5742], "Father&#39;s Mobile:", [5726, 5742], "Father&rsquo;s Mobile:", [5957, 5973], "Mother&apos;s Mobile:", [5957, 5973], "Mother&lsquo;s Mobile:", [5957, 5973], "Mother&#39;s Mobile:", [5957, 5973], "Mother&rsquo;s Mobile:"]